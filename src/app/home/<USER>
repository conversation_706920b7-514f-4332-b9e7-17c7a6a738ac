"use client"

import React from 'react';
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";

const Page = () => {
    const { data: session, status } = useSession();
    const router = useRouter();

    if (status === "loading") {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-lg">Loading...</div>
            </div>
        );
    }

    if (status === "unauthenticated") {
        router.push('/auth/login');
        return null;
    }

    const handleLogout = async () => {
        await signOut({ callbackUrl: '/auth/login' });
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <nav className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between h-16">
                        <div className="flex items-center">
                            <h1 className="text-xl font-semibold text-gray-900">HSMS Admin Panel</h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <span className="text-sm text-gray-700">
                                Welcome, {session?.user?.name}
                            </span>
                            <button
                                onClick={handleLogout}
                                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                            >
                                Logout
                            </button>
                        </div>
                    </div>
                </div>
            </nav>

            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                    <div className="bg-white overflow-hidden shadow rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <h2 className="text-lg font-medium text-gray-900 mb-4">Dashboard</h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="bg-blue-50 p-4 rounded-lg">
                                    <h3 className="text-sm font-medium text-blue-800">User Information</h3>
                                    <div className="mt-2 text-sm text-blue-600">
                                        <p><strong>Name:</strong> {session?.user?.name}</p>
                                        <p><strong>Email:</strong> {session?.user?.email}</p>
                                        <p><strong>Role:</strong> {session?.user?.role}</p>
                                        <p><strong>User ID:</strong> {session?.user?.id}</p>
                                    </div>
                                </div>
                                <div className="bg-green-50 p-4 rounded-lg">
                                    <h3 className="text-sm font-medium text-green-800">Quick Actions</h3>
                                    <div className="mt-2 space-y-2">
                                        <button className="block w-full text-left px-3 py-2 text-sm text-green-700 hover:bg-green-100 rounded">
                                            View Appointments
                                        </button>
                                        <button className="block w-full text-left px-3 py-2 text-sm text-green-700 hover:bg-green-100 rounded">
                                            Manage Doctors
                                        </button>
                                        <button className="block w-full text-left px-3 py-2 text-sm text-green-700 hover:bg-green-100 rounded">
                                            Hospital Settings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
};

export default Page;