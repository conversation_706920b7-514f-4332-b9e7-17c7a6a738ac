"use client"

import React, { useState } from 'react';
import AllHospitals from "@/components/hospital/AllHospitals";
import CreateButton from "@/components/CreateButton";
import HospitalModal from "@/components/HospitalModal";
import {FA_Icons} from "@/icons/icons";

const Page = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const handleAddHospital = (formData: any) => {
        console.log('New hospital data:', formData);
        // Here you would typically send the data to your backend API
        // For now, we'll just log it to the console

        // You can also trigger a refresh of the AllHospitals component
        // or update a global state management system
    };

    return (
        <div className="p-6">
            <div className="mb-6 flex justify-between items-center">
                <div>
                <h1 className="text-2xl font-bold text-gray-900">Hospital Management</h1>
                <p className="text-gray-600 mt-1">Manage hospital settings and configurations</p>
                </div>
                <CreateButton
                    suffixSting={"Hospital"}
                    icon ={FA_Icons.HOSPITAL}
                    onClick={() => setIsModalOpen(true)}
                />
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
                <AllHospitals/>
                {/*<h3 className="text-lg font-medium text-gray-900 mb-4">Hospital Settings</h3>*/}
                {/*<p className="text-gray-600">Hospital management features will be implemented here.</p>*/}
            </div>

            {/* Hospital Modal */}
            <HospitalModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onSubmit={handleAddHospital}
            />
        </div>
    );
};

export default Page;